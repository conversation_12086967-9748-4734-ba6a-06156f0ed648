"""
Environment checker for v-switch agent.
"""

import os
import subprocess
import logging
from typing import List, Tu<PERSON>, Dict, Any
import time
from datetime import datetime
from pathlib import Path


class EnvironmentChecker:
    """Checks system environment for v-switch agent requirements."""

    LAST_SEEN_FILE = Path("./service_last_seen.timestamp")

    def __init__(self, env_check_config):
        """Initialize environment checker.

        Args:
            env_check_config: Environment check configuration object
        """
        self.logger = logging.getLogger(__name__)
        self.env_check_config = env_check_config
    
    def check_all(self) -> Tuple[bool, List[str]]:
        """
        检查所有环境要求

        Returns:
            Tuple of (success, error_messages)
        """
        errors = []

        # Check base environment (IP forwarding, RP filter, basic commands)
        if self.env_check_config.base:
            # Check IP forwarding
            if not self.check_ip_forwarding():
                errors.append("IP forwarding is not enabled (net.ipv4.ip_forward = 1)")

            # Check RP filter settings
            rp_filter_errors = self.check_rp_filter()
            if rp_filter_errors:
                errors.extend(rp_filter_errors)

            # Check basic required commands
            missing_commands = self.check_basic_commands()
            if missing_commands:
                errors.append(f"Missing basic commands: {', '.join(missing_commands)}")
        else:
            self.logger.debug("Base environment check disabled, skipping")

        # Check OVS installation
        if self.env_check_config.ovs:
            if not self.check_ovs():
                errors.append("Open vSwitch (OVS) is not installed or not accessible")
        else:
            self.logger.debug("OVS environment check disabled, skipping")

        # Check nftables installation
        if self.env_check_config.nftables:
            if not self.check_nftables():
                errors.append("nftables is not installed or not accessible")
        else:
            self.logger.debug("nftables environment check disabled, skipping")

        success = len(errors) == 0
        return success, errors
    
    def check_ovs(self) -> bool:
        """
        检查OVS是否安装
        
        Returns:
            True if OVS is available
        """
        try:
            # Try to run ovs-vsctl version
            result = subprocess.run(
                ['ovs-vsctl', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                self.logger.debug("OVS check passed")
                return True
            else:
                self.logger.error(f"OVS check failed: {result.stderr}")
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError) as e:
            self.logger.error(f"OVS check failed: {e}")
            return False
    
    def check_ip_forwarding(self) -> bool:
        """
        检查IP转发是否开启
        
        Returns:
            True if IP forwarding is enabled
        """
        try:
            with open('/proc/sys/net/ipv4/ip_forward', 'r') as f:
                value = f.read().strip()
            
            if value == '1':
                self.logger.debug("IP forwarding check passed")
                return True
            else:
                self.logger.error(f"IP forwarding is disabled (value: {value})")
                return False
                
        except (FileNotFoundError, PermissionError, IOError) as e:
            self.logger.error(f"IP forwarding check failed: {e}")
            return False
    
    def check_rp_filter(self) -> List[str]:
        """
        检查RP过滤器设置
        
        Returns:
            List of error messages (empty if all checks pass)
        """
        errors = []
        
        # Check net.ipv4.conf.all.rp_filter = 0
        try:
            with open('/proc/sys/net/ipv4/conf/all/rp_filter', 'r') as f:
                value = f.read().strip()
            
            if value != '0':
                errors.append(f"net.ipv4.conf.all.rp_filter should be 0 (current: {value})")
            else:
                self.logger.debug("RP filter all check passed")
                
        except (FileNotFoundError, PermissionError, IOError) as e:
            errors.append(f"Failed to check net.ipv4.conf.all.rp_filter: {e}")
        
        # Check net.ipv4.conf.default.rp_filter = 0
        try:
            with open('/proc/sys/net/ipv4/conf/default/rp_filter', 'r') as f:
                value = f.read().strip()
            
            if value != '0':
                errors.append(f"net.ipv4.conf.default.rp_filter should be 0 (current: {value})")
            else:
                self.logger.debug("RP filter default check passed")
                
        except (FileNotFoundError, PermissionError, IOError) as e:
            errors.append(f"Failed to check net.ipv4.conf.default.rp_filter: {e}")
        
        return errors
    
    def check_basic_commands(self) -> List[str]:
        """
        检查基础命令是否可用

        Returns:
            List of missing commands
        """
        basic_commands = [
            'ip'
        ]

        return self._check_commands(basic_commands)

    def check_nftables(self) -> bool:
        """
        检查nftables是否安装和可用

        Returns:
            True if nftables is available
        """
        try:
            # Check if nft command exists
            result = subprocess.run(
                ['which', 'nft'],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode != 0:
                self.logger.error("nft command not found")
                return False

            # Check if nftables service is available
            result = subprocess.run(
                ['nft', 'list', 'tables'],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                self.logger.error(f"nftables not accessible: {result.stderr}")
                return False

            self.logger.debug("nftables is available")
            return True

        except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
            self.logger.error(f"Failed to check nftables: {e}")
            return False

    def _check_commands(self, commands: List[str]) -> List[str]:
        """
        检查指定命令是否可用

        Args:
            commands: List of commands to check

        Returns:
            List of missing commands
        """
        missing = []

        for cmd in commands:
            try:
                result = subprocess.run(
                    ['which', cmd],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if result.returncode != 0:
                    missing.append(cmd)
                else:
                    self.logger.debug(f"Command {cmd} found")

            except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
                self.logger.error(f"Failed to check command {cmd}: {e}")
                missing.append(cmd)

        return missing

    # 检查主机最近是否重启过, 如果是，返回True, 否则返回False
    def check_reboot(self) -> bool:
      system_boot_time = self.get_system_boot_time()
      if system_boot_time is None:
          # 无法获取系统启动时间, 抛出异常
          raise Exception("Failed to get system boot time")
      
      if self.LAST_SEEN_FILE.exists():
          try:
              last_seen_time = float(self.LAST_SEEN_FILE.read_text())
          except (ValueError, OSError):
              # 如果文件内容损坏或无法读取，当作首次运行
              last_seen_time = 0.0

      print(f"System boot timestamp: {system_boot_time}")
      print(f"Service last seen timestamp: {last_seen_time}")
      # 无论是否重启，都更新“最后一次见到”的时间戳
      # 这样可以确保下一次启动时有正确的比较基准
      self.LAST_SEEN_FILE.write_text(str(time.time()))
      return system_boot_time > last_seen_time


    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            Dictionary with system information
        """
        info = {}
        
        # Get kernel version
        try:
            with open('/proc/version', 'r') as f:
                info['kernel_version'] = f.read().strip()
        except Exception:
            info['kernel_version'] = "Unknown"
        
        # Get OS release
        try:
            with open('/etc/os-release', 'r') as f:
                os_release = {}
                for line in f:
                    if '=' in line:
                        key, value = line.strip().split('=', 1)
                        os_release[key] = value.strip('"')
                info['os_release'] = os_release
        except Exception:
            info['os_release'] = {}
        
        # Get network interfaces
        try:
            result = subprocess.run(
                ['ip', 'link', 'show'],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                info['network_interfaces'] = result.stdout
        except Exception:
            info['network_interfaces'] = "Failed to get network interfaces"
        
        return info

    def get_system_boot_time():
        """
        通过执行 'uptime -s' 命令获取系统启动时间的时间戳。
        返回:
            float: 系统启动时间的 Unix 时间戳。
            None: 如果命令执行或解析失败。
        """
        # 执行命令 'uptime -s'，它会输出类似 '2025-08-22 03:10:00' 的格式
        # text=True 让输出为字符串，capture_output=True 捕获输出，check=True 如果命令失败则抛出异常
        result = subprocess.run(
            ['uptime', '-s'],
            check=True,
            capture_output=True,
            text=True
        )
        
        # 获取命令的标准输出，并去除末尾的换行符
        boot_time_str = result.stdout.strip()
        
        # 将 'YYYY-MM-DD HH:MM:SS' 格式的字符串解析为 datetime 对象
        boot_time_dt = datetime.strptime(boot_time_str, '%Y-%m-%d %H:%M:%S')
        
        # 将 datetime 对象转换为 Unix 时间戳 (float)
        return boot_time_dt.timestamp()