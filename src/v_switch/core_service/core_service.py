"""
Core service for v-switch.
"""

import logging
import threading
from typing import Optional

from v_switch.common.models import ServerMetadata
from v_switch.common.etcd_client import ETCDClient
from v_switch.config.server_config import ServerConfig
from v_switch.core_service.shard_manager import Shard<PERSON>anager
from v_switch.core_service.agent_manager import Agent<PERSON><PERSON><PERSON>
from v_switch.core_service.subnet_service import SubnetService
from v_switch.core_service.ip_allocator import IPAllocator


class CoreService:
    """主要核心服务类别协调所有组件。"""

    def __init__(self, config: ServerConfig):
        """初始化核心服务。

        Args:
            config: Server configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 初始化 ETCD 客户端
        self.etcd_client = ETCDClient(
            host=config.etcd.host,
            port=config.etcd.port,
            timeout=config.etcd.timeout,
            username=config.etcd.username,
            password=config.etcd.password
        )

        # 初始化管理器
        self.shard_manager = ShardManager(self.etcd_client, config)
        self.agent_manager = AgentManager(self.etcd_client, config)
        self.mon_ip_allocator = IPAllocator(
            ip_min=config.monnet.ip_min,
            ip_max=config.monnet.ip_max,
            etcd_client=self.etcd_client,
            key="/network/server/mon_ips"
        )
        self.subnet_service = SubnetService(self.etcd_client, config, self.shard_manager, self.mon_ip_allocator, self.agent_manager)

        # 多线程
        self._agent_monitor_thread = None
        self._running = False

    def start(self) -> bool:
        """启动核心服务。

        Returns:
            True if successful
        """
        try:
            self.logger.info("Initializing core service...")

            # 连接到 ETCD
            self.etcd_client.connect()

            # 初始化分片
            if not self.shard_manager.initialize_shards():
                self.logger.error("Failed to initialize shards")
                return False

            # 初始化元数据
            if not self.agent_manager.initialize_metadata():
                self.logger.error("Failed to initialize metadata")
                return False

            self.logger.info("Core service initialized successfully")

            self._running = True

            self._run_agent_monitoring()
            self._run_agent_task_monitoring()
            self.logger.info("Core service started successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start core service: {e}")
            return False

    def stop(self) -> None:
        """停止核心服务。"""
        try:
            self.logger.info("Stopping core service...")
            self._running = False

            # 等待 agent 监控线程完成
            if self._agent_monitor_thread and self._agent_monitor_thread.is_alive():
                self._agent_monitor_thread.join(timeout=5.0)

            self.logger.info("Core service stopped")

        except Exception as e:
            self.logger.error(f"Error stopping core service: {e}")

    def _run_agent_monitoring(self) -> None:
        """在后台线程中运行 agent 监控。"""
        try:
            self._agent_monitor_thread = threading.Thread(
                target=self.agent_manager.start_agent_monitoring,
                name="AgentMonitor",
                daemon=True
            )
            self._agent_monitor_thread.start()
            # self.agent_manager.start_agent_monitoring()
        except Exception as e:
            self.logger.error(f"Agent monitoring thread error: {e}")

    def _run_agent_task_monitoring(self) -> None:
        """在后台线程中运行 agent 任务监控。"""
        try:
            self._agent_monitor_thread = threading.Thread(
                target=self.agent_manager.start_task_monitoring,
                name="TaskMonitor",
                daemon=True
            )
            self._agent_monitor_thread.start()
        except Exception as e:
            self.logger.error(f"Agent task monitoring thread error: {e}")

    def create_subnet(self, tenant_id: str, 
                      vlan_id: int, 
                      subnet_gw_ip: str,
                      subnet_ip_start: Optional[str] = None, 
                      subnet_ip_end: Optional[str] = None, 
                      subnet_ip_mask: Optional[int] = 24) -> Optional[str]:
        """创建子网。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP

        Returns:
            Subnet ID if successful, None otherwise
        """
        return self.subnet_service.create_subnet(tenant_id=tenant_id, 
                                                 vlan_id=vlan_id, 
                                                 subnet_gw_ip=subnet_gw_ip, 
                                                 subnet_ip_start=subnet_ip_start, 
                                                 subnet_ip_end=subnet_ip_end, 
                                                 subnet_ip_mask=subnet_ip_mask)

    def query_subnet(self, tenant_id: str, vlan_id: int) -> Optional[dict]:
        """查询子网。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier

        Returns:
            Subnet configuration or None if not found
        """
        return self.subnet_service.query_subnet(tenant_id, vlan_id)

    def modify_subnet(self, tenant_id: str, 
                      vlan_id: int, 
                      subnet_gw_ip: str,
                      subnet_ip_start: Optional[str] = None, 
                      subnet_ip_end: Optional[str] = None, 
                      subnet_ip_mask: Optional[int] = 24) -> Optional[str]:
        """创建子网。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP

        Returns:
            Subnet ID if successful, None otherwise
        """
        return self.subnet_service.modify_subnet(tenant_id=tenant_id, 
                                                 vlan_id=vlan_id, 
                                                 subnet_gw_ip=subnet_gw_ip, 
                                                 subnet_ip_start=subnet_ip_start, 
                                                 subnet_ip_end=subnet_ip_end, 
                                                 subnet_ip_mask=subnet_ip_mask)


    def delete_subnet(self, tenant_id: str, vlan_id: int) -> bool:
        """删除子网。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier

        Returns:
            True if successful
        """
        return self.subnet_service.delete_subnet(tenant_id, vlan_id)

    def mount_eip(self, tenant_id: str, vlan_id: int, 
                  eip: str, internal_ip: str, 
                  eip_gateway: Optional[str] = None,
                  rate: Optional[str] = None,
                  ceil: Optional[str] = None) -> bool:
        """挂载EIP。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            internal_ip: Internal IP address
            eip_gateway: EIP gateway (optional)

        Returns:
            True if successful
        """
        self.logger.info(f"mount_eip Params: {tenant_id}, {vlan_id}, {eip}, {internal_ip}, {eip_gateway}, {rate}, {ceil}")
        return self.subnet_service.mount_eip(tenant_id=tenant_id, 
                                             vlan_id=vlan_id, 
                                             eip=eip, 
                                             internal_ip=internal_ip, 
                                             eip_gateway=eip_gateway,
                                             rate=rate,
                                             ceil=ceil)
    
    def modify_eip(self, tenant_id: str, vlan_id: int, 
                  eip: str, rate: str, ceil: str) -> bool:
        """修改EIP限速。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            rate: 保证带宽, 这是该EIP在任何情况下都能获得的最低速率。不填则默认满带宽
            ceil: 最高带宽, 当有空闲带宽时，该EIP可以借用带宽，但不能超过这个上限。不填则默认满带宽

        Returns:
            True if successful
        """
        return self.subnet_service.modify_eip(tenant_id=tenant_id,
                                             vlan_id=vlan_id, 
                                             eip=eip, 
                                             rate=rate,
                                             ceil=ceil)

    def unmount_eip(self, tenant_id: str, vlan_id: int, eip: str, internal_ip: str) -> bool:
        """卸载EIP。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            internal_ip: Internal IP address

        Returns:
            True if successful
        """
        return self.subnet_service.unmount_eip(tenant_id, vlan_id, eip, internal_ip)

    def create_eip_snat(self, tenant_id: str, vlan_id: int, eip: str, gateway_ip: str, mask: int = 24) -> bool:
        """创建EIP SNAT。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            mask: Subnet mask (optional, defaults to 24)

        Returns:
            True if successful
        """
        return self.subnet_service.create_eip_snat(tenant_id, vlan_id, eip, gateway_ip, mask)

    def delete_eip_snat(self, tenant_id: str, vlan_id: int, eip: str, gateway_ip: str, mask: int = 24) -> bool:
        """删除EIP SNAT。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            mask: Subnet mask (optional, defaults to 24)

        Returns:
            True if successful
        """
        return self.subnet_service.delete_eip_snat(tenant_id, vlan_id, eip, gateway_ip, mask)

    def create_eip_dnat(self, tenant_id: str, vlan_id: int, eip: str, internal_ip: str, type: str, dport: int, port: int) -> bool:
        """创建EIP DNAT。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            internal_ip: Internal IP address
            type: Protocol type (tcp or udp)
            dport: External port
            port: Internal port

        Returns:
            True if successful
        """
        return self.subnet_service.create_eip_dnat(tenant_id, vlan_id, eip, internal_ip, type, dport, port)

    def delete_eip_dnat(self, tenant_id: str, vlan_id: int, eip: str, internal_ip: str, type: str, dport: int, port: int) -> bool:
        """删除EIP DNAT。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            internal_ip: Internal IP address
            type: Protocol type (tcp or udp)
            dport: External port
            port: Internal port

        Returns:
            True if successful
        """
        return self.subnet_service.delete_eip_dnat(tenant_id, vlan_id, eip, internal_ip, type, dport, port)

    def get_server_metadata(self) -> Optional[ServerMetadata]:
        """获取服务器元数据。

        Returns:
            Server metadata or None if not found
        """
        return self.agent_manager.get_metadata()


    def is_running(self) -> bool:
        """检查核心服务是否正在运行。

        Returns:
            True if running
        """
        return self._running
