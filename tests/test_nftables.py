import nftables
import json

# 1. 创建 nftables 实例
nft = nftables.Nftables()

# 2. 用 JSON 定义指令
command = {
  "nftables": [
    {
      "add": {
        "rule": {
          "family": "ip",
          "table": "filter",
          "chain": "input",
          "expr": [
            {
              "match": {
                "name": "ip",
                "left": { "payload": { "protocol": "ip", "field": "saddr" } },
                "right": "*******"
              }
            },
            { "accept": None }
          ]
        }
      }
    }
  ]
}

# 3. 执行指令并检查结果
rc, output, error = nft.json_cmd(command)

if rc == 0:
  print("规则添加成功!")
else:
  print(f"出错了: {error}")