class IpUtils:
  
  @staticmethod
  def ip_to_int(ip):
    """将IP地址转换为整数"""
    ip_parts = ip.split('.')
    if len(ip_parts) != 4:
      raise ValueError("Invalid IP address")
    return int(ip_parts[0]) << 24 | int(ip_parts[1]) << 16 | int(ip_parts[2]) << 8 | int(ip_parts[3])

  @staticmethod
  def int_to_ip(ip_int):
    """将整数转换为IP地址"""
    if ip_int < 0 or ip_int > 0xFFFFFFFF:
      raise ValueError("Invalid IP address")
    return f"{ip_int >> 24}.{(ip_int >> 16) & 0xFF}.{(ip_int >> 8) & 0xFF}.{ip_int & 0xFF}"
  

# if __name__ == "__main__":

#   ip_int = IpUtils.ip_to_int("**************")
#   print(ip_int)
#   print(IpUtils.int_to_ip(ip_int))
