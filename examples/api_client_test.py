#!/usr/bin/env python3
"""
API client test script for v-switch FastAPI server.
"""

import requests
import json
import time
import sys
from typing import Dict, Any


class VSwitchAPIClient:
    """Simple API client for v-switch server."""
    
    def __init__(self, base_url: str = "http://localhost:30090"):
        """Initialize API client.
        
        Args:
            base_url: Base URL of the API server
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def health_check(self) -> Dict[str, Any]:
        """Check server health."""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    def get_status(self) -> Dict[str, Any]:
        """Get server status."""
        response = self.session.get(f"{self.base_url}/status")
        response.raise_for_status()
        return response.json()
    
    def create_subnet_gateway(self, tenant_id: str, vlan_id: int, subnet_gw_ip: str) -> Dict[str, Any]:
        """Create subnet gateway."""
        data = {
            "tenant_id": tenant_id,
            "vlan_id": vlan_id,
            "subnet_gw_ip": subnet_gw_ip
        }
        response = self.session.post(f"{self.base_url}/network/subnet-gateway", json=data)
        response.raise_for_status()
        return response.json()
    
    def create_eip(self, tenant_id: str, vlan_id: int, eip: str, 
                   gateway_ip: str, internal_ip: str) -> Dict[str, Any]:
        """Create EIP."""
        data = {
            "tenant_id": tenant_id,
            "vlan_id": vlan_id,
            "eip": eip,
            "gateway_ip": gateway_ip,
            "internal_ip": internal_ip
        }
        response = self.session.post(f"{self.base_url}/network/eip", json=data)
        response.raise_for_status()
        return response.json()
    
    def get_instruction_status(self, request_id: str, tenant_id: str) -> Dict[str, Any]:
        """Get instruction status."""
        params = {"tenant_id": tenant_id}
        response = self.session.get(f"{self.base_url}/instruction/{request_id}", params=params)
        response.raise_for_status()
        return response.json()
    
    def delete_instruction(self, request_id: str, tenant_id: str) -> Dict[str, Any]:
        """Delete instruction."""
        params = {"tenant_id": tenant_id}
        response = self.session.delete(f"{self.base_url}/instruction/{request_id}", params=params)
        response.raise_for_status()
        return response.json()


def test_api_functionality():
    """Test API functionality."""
    client = VSwitchAPIClient()
    
    print("Testing v-switch FastAPI server...")
    print("=" * 50)
    
    # Test health check
    try:
        print("1. Testing health check...")
        result = client.health_check()
        print(f"   ✓ Health: {json.dumps(result, indent=2)}")
    except Exception as e:
        print(f"   ✗ Health check failed: {e}")
        return False
    
    # Test server status
    try:
        print("\n2. Testing server status...")
        result = client.get_status()
        print(f"   ✓ Status: {json.dumps(result, indent=2)}")
    except Exception as e:
        print(f"   ✗ Server status failed: {e}")
        return False
    
    # Test create subnet gateway
    try:
        print("\n3. Testing create subnet gateway...")
        result = client.create_subnet_gateway(
            tenant_id="test-tenant-001",
            vlan_id=100,
            subnet_gw_ip="*************"
        )
        print(f"   ✓ Subnet Gateway: {json.dumps(result, indent=2)}")
        subnet_request_id = result.get("request_id")
    except Exception as e:
        print(f"   ✗ Create subnet gateway failed: {e}")
        subnet_request_id = None
    
    # Test create EIP
    try:
        print("\n4. Testing create EIP...")
        result = client.create_eip(
            tenant_id="test-tenant-001",
            vlan_id=100,
            eip="************",
            gateway_ip="*************",
            internal_ip="*************0"
        )
        print(f"   ✓ EIP: {json.dumps(result, indent=2)}")
        eip_request_id = result.get("request_id")
    except Exception as e:
        print(f"   ✗ Create EIP failed: {e}")
        eip_request_id = None
    
    # Test get instruction status
    if subnet_request_id:
        try:
            print(f"\n5. Testing get instruction status (subnet gateway)...")
            result = client.get_instruction_status(subnet_request_id, "test-tenant-001")
            print(f"   ✓ Instruction Status: {json.dumps(result, indent=2)}")
        except Exception as e:
            print(f"   ✗ Get instruction status failed: {e}")
    
    # Test delete instruction
    if eip_request_id:
        try:
            print(f"\n6. Testing delete instruction (EIP)...")
            result = client.delete_instruction(eip_request_id, "test-tenant-001")
            print(f"   ✓ Delete Instruction: {json.dumps(result, indent=2)}")
        except Exception as e:
            print(f"   ✗ Delete instruction failed: {e}")
    
    print("\n" + "=" * 50)
    print("API testing completed!")
    return True


def wait_for_server(client: VSwitchAPIClient, timeout: int = 30) -> bool:
    """Wait for server to be ready."""
    print(f"Waiting for server to be ready (timeout: {timeout}s)...")
    
    for i in range(timeout):
        try:
            client.health_check()
            print("✓ Server is ready!")
            return True
        except Exception:
            if i < timeout - 1:
                time.sleep(1)
                print(".", end="", flush=True)
    
    print("\n✗ Server not ready within timeout")
    return False


def main():
    """Main function."""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:30090"
    
    client = VSwitchAPIClient(base_url)
    
    # Wait for server to be ready
    if not wait_for_server(client):
        return 1
    
    # Run tests
    if test_api_functionality():
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
