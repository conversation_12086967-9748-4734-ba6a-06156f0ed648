

import json
import re

def config_to_commands(config):
    """
    Converts a subnet configuration dictionary to a list of shell commands
    in the correct order.
    """
    commands = []
    vlan_id = config.get("vlan_id")
    if not vlan_id:
        raise ValueError("vlan_id is a required field in the config")

    namespaces_config = config.get("namespaces", [])
    devices = config.get("devices", {})

    # --- 1. Create Namespaces ---
    for ns in namespaces_config:
        ns_name = ns.get("name")
        if ns_name:
            commands.append(f"ip netns add {ns_name}")

    # --- 2. Create veth pairs, move to namespace, assign IPs, and attach to OVS ---
    for veth in devices.get("veth_pairs", []):
        name = veth.get("name")
        peer = veth.get("peer")
        peer_ns = veth.get("peer_namespace")

        if not (name and peer and peer_ns):
            continue

        # a. Create the veth pair
        commands.append(f"ip link add {name} type veth peer name {peer}")

        # b. Move the peer into its namespace
        commands.append(f"ip link set {peer} netns {peer_ns}")

        # c. Find the corresponding interface and assign IP
        for ns in namespaces_config:
            if ns.get("name") == peer_ns:
                for iface in ns.get("interfaces", []):
                    if iface.get("name") == peer:
                        for addr in iface.get("addresses", []):
                            ip = addr.get("ip")
                            prefix = addr.get("prefix")
                            if ip and prefix:
                                commands.append(f"ip netns exec {peer_ns} ip addr add {ip}/{prefix} dev {peer}")

        # d. Attach to OVS bridges
        for ovs_port in devices.get("ovs_ports", []):
            if ovs_port.get("port") == name:
                bridge = ovs_port.get("bridge")
                tag = ovs_port.get("tag")
                if bridge and tag:
                    commands.append(f"ovs-vsctl add-port {bridge} {name} tag={tag}")
                elif bridge:
                    commands.append(f"ovs-vsctl add-port {bridge} {name}")

    # --- 3. Bring Interfaces Up ---
    # Host-side veths
    for veth in devices.get("veth_pairs", []):
        if veth.get("state") == "up":
            commands.append(f'ip link set {veth.get("name")} up')

    # Namespace-side interfaces
    for ns in namespaces_config:
        ns_name = ns.get("name")
        for iface in ns.get("interfaces", []):
            iface_name = iface.get("name")
            # Bring up interfaces marked as "up"
            if iface.get("state") == "up" and ns_name and iface_name:
                commands.append(f'ip netns exec {ns_name} ip link set {iface_name} up')
            # Also bring up interfaces that have an IP assigned, as they should be up
            elif 'addresses' in iface and iface.get("name") != 'lo':
                commands.append(f'ip netns exec {ns_name} ip link set {iface.get("name")} up')


    # --- 4. Add Routes ---
    for route in config.get("routes", []):
        namespace = route.get("namespace")
        destination = route.get("destination")
        gateway = route.get("gateway")
        device = route.get("device")
        if namespace and destination and gateway and device:
            commands.append(f"ip netns exec {namespace} ip route add {destination} via {gateway} dev {device}")

    # --- 5. Add NFTables Rules ---
    nft_config = config.get("nft", {})
    if nft_config:
        ns_name = nft_config.get("namespace")
        for table in nft_config.get("tables", []):
            table_family = table.get("family")
            table_name = table.get("name")
            if ns_name and table_family and table_name:
                commands.append(f'ip netns exec {ns_name} nft add table {table_family} {table_name}')
            for chain in table.get("chains", []):
                chain_name = chain.get("name")
                chain_type = chain.get("type")
                chain_hook = chain.get("hook")
                chain_priority = chain.get("priority")
                if ns_name and chain_name and chain_type and chain_hook is not None and chain_priority is not None:
                    commands.append(f'ip netns exec {ns_name} nft add chain {table_family} {table_name} {chain_name} {{ type {chain_type} hook {chain_hook} priority {chain_priority} \; }}')
                for rule in chain.get("rules", []):
                    rule_str = f'ip netns exec {ns_name} nft add rule {table_family} {table_name} {chain_name}'
                    if "match" in rule:
                        match_parts = []
                        if "ip" in rule["match"]:
                            for k, v in rule["match"]["ip"].items():
                                match_parts.append(f"ip {k} {v}")
                        if "tcp" in rule["match"]:
                            for k, v in rule["match"]["tcp"].items():
                                match_parts.append(f"tcp {k} {v}")
                        rule_str += " " + " ".join(match_parts)

                    if "action" in rule:
                        action_name = list(rule["action"].keys())[0]
                        action_params = rule["action"][action_name]
                        if isinstance(action_params, dict):
                            to_val = action_params.get("to")
                            rule_str += f" {action_name} to {to_val}"
                        else:
                            rule_str += f" {action_name}"

                    if rule.get("counter"):
                        rule_str += " counter"
                    commands.append(rule_str)

    # --- 6. Final verification command ---
    for ns in config.get("namespaces", []):
        ns_name = ns.get("name")
        if ns_name:
            commands.append(f"ip netns exec {ns_name} ip addr")

    return commands


''' 命令样例
  # 创建一个专门用于此VLAN的网络命名空间，以隔离网络环境
  ip netns add ns-vlan${VLAN_ID}

  # 创建一对虚拟以太网设备（veth pair），用于连接主机和网络命名空间
  ip link add v-lan-host-${VLAN_ID} type veth peer name v-lan-ns-${VLAN_ID}
  # 将veth pair的一端（v-lan-ns-）移动到新创建的网络命名空间中
  ip link set v-lan-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
  # 在网络命名空间内，为veth设备配置IP地址，作为该VLAN子网的网关
  ip netns exec ns-vlan${VLAN_ID} ip addr add ${ZW_GA_WAY_IP}/24 dev v-lan-ns-${VLAN_ID}
  # 将veth pair在主机侧的一端（v-lan-host-）添加到OVS网桥br-vlan，并打上VLAN标签
  ovs-vsctl add-port br-vlan v-lan-host-${VLAN_ID}  tag=${VLAN_ID}

  # 创建另一对veth pair，用于弹性IP（EIP）的连接
  ip link add v-eip-host-${VLAN_ID} type veth peer name v-eip-ns-${VLAN_ID}
  # 将EIP veth pair的一端移动到VLAN的网络命名空间中
  ip link set v-eip-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
  # 将EIP veth pair在主机侧的一端添加到OVS网桥br-eip
  ovs-vsctl add-port br-eip v-eip-host-${VLAN_ID}

  # 创建用于监控的veth pair
  ip link add v-mon-host-${VLAN_ID} type veth peer name v-mon-ns-${VLAN_ID}
  # 将监控veth pair的一端移动到VLAN的网络命名空间中
  ip link set v-mon-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
  # 在网络命名空间内为监控veth设备配置IP地址
  ip netns exec ns-vlan${VLAN_ID} ip addr add ${MON_IP}/24 dev v-mon-ns-${VLAN_ID}
  # 将监控veth pair在主机侧的一端添加到OVS网桥br-mon
  ovs-vsctl add-port br-mon v-mon-host-${VLAN_ID}

  #-------------- 统一启动网络接口
  # 启动主机侧的veth接口
  ip link set v-lan-host-${VLAN_ID} up
  # 启动网络命名空间内的veth接口
  ip netns exec ns-vlan${VLAN_ID} ip link set v-lan-ns-${VLAN_ID} up
  # 启动EIP veth pair的两端接口
  ip link set v-eip-host-${VLAN_ID} up
  ip netns exec ns-vlan${VLAN_ID} ip link set v-eip-ns-${VLAN_ID} up
  # 启动监控veth pair的两端接口
  ip link set v-mon-host-${VLAN_ID} up
  ip netns exec ns-vlan${VLAN_ID} ip link set v-mon-ns-${VLAN_ID} up
  # 启动网络命名空间内的本地环回接口
  ip netns exec ns-vlan${VLAN_ID} ip link set lo up

  # 在命名空间内添加一条nftables规则，对发往同一子网的流量进行源地址转换（SNAT），将其源IP伪装成网关IP
  ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat POSTROUTING ip saddr ${PRE_GA_WAY_IP}.0/24 ip daddr ${PRE_GA_WAY_IP}.0/${ZW_MASK} counter snat to ${ZW_GA_WAY_IP}
  # 在命名空间内创建nftables的nat表和PREROUTING链，用于处理入站流量的目的地址转换
  ip netns exec ns-vlan${VLAN_ID} nft add chain ip nat PREROUTING { type nat hook prerouting priority -100 \; }
  # 添加DNAT规则，将对特定IP（***************，通常用于云元数据服务）443端口的访问重定向到内部的监控管理服务
  ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat PREROUTING ip daddr *************** tcp dport 443 counter dnat to ${MON_M_IP}:${MON_M_PORT}
  # 添加SNAT规则，将访问监控管理服务的出站流量的源IP伪装成监控IP
  ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 30428 counter snat to ************
  # 允许访问监控管理服务的69端口（TFTP）和9879端口，同样进行SNAT
  ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 69 counter snat to ************
  ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 9879 counter snat to ************

  # 显示网络命名空间内的IP地址配置，以供验证
  ip netns exec ns-vlan${VLAN_ID} ip addr
'''
if __name__ == "__main__":
    # Example usage with the JSON from the documentation
    sample_config_str = """
    {
      "tenant_id": "tenant-1",
      "vlan_id": "1003",
      "namespaces": [
        {
          "name": "ns-vlan1003",
          "interfaces": [
            {
              "name": "v-lan-ns-1003",
              "addresses": [
                {
                  "ip": "***********",
                  "prefix": 24
                }
              ]
            },
            {
              "name": "v-eip-ns-1003",
              "addresses": [
                {
                  "ip": "**************",
                  "prefix": 24
                }
              ]
            },
            {
              "name": "v-mon-ns-1003",
              "addresses": [
                {
                  "ip": "************",
                  "prefix": 24
                }
              ]
            },
            {
              "name": "lo",
              "state": "up"
            }
          ]
        }
      ],
      "devices": {
        "veth_pairs": [
          {
            "name": "v-lan-host-1003",
            "peer": "v-lan-ns-1003",
            "peer_namespace": "ns-vlan1003",
            "state": "up"
          },
          {
            "name": "v-eip-host-1003",
            "peer": "v-eip-ns-1003",
            "peer_namespace": "ns-vlan1003",
            "state": "up"
          },
          {
            "name": "v-mon-host-1003",
            "peer": "v-mon-ns-1003",
            "peer_namespace": "ns-vlan1003",
            "state": "up"
          }
        ],
        "ovs_ports": [
          {
            "bridge": "br-vlan",
            "port": "v-lan-host-1003",
            "tag": 1003
          },
          {
            "bridge": "br-eip",
            "port": "v-eip-host-1003"
          },
          {
            "bridge": "br-mon",
            "port": "v-mon-host-1003"
          }
        ]
      },
      "routes": [
        {
          "namespace": "ns-vlan1003",
          "destination": "default",
          "gateway": "*************",
          "device": "v-eip-ns-1003"
        }
      ],
      "nft": {
        "namespace": "ns-vlan1003",
        "tables": [
          {
            "family": "ip",
            "name": "nat",
            "chains": [
              {
                "name": "PREROUTING",
                "type": "nat",
                "hook": "prerouting",
                "priority": -100,
                "rules": [
                  {
                    "match": {
                      "ip": {
                        "daddr": "***************"
                      },
                      "tcp": {
                        "dport": 443
                      }
                    },
                    "action": {
                      "dnat": {
                        "to": "*************:30428"
                      }
                    },
                    "counter": true
                  },
                  {
                    "match": {
                      "ip": {
                        "daddr": "**************"
                      }
                    },
                    "action": {
                      "dnat": {
                        "to": "***********"
                      }
                    },
                    "counter": true
                  }
                ]
              },
              {
                "name": "POSTROUTING",
                "type": "nat",
                "hook": "postrouting",
                "priority": 100,
                "rules": [
                  {
                    "match": {
                      "ip": {
                        "saddr": "***********/24",
                        "daddr": "***********/24"
                      }
                    },
                    "action": {
                      "snat": {
                        "to": "***********"
                      }
                    },
                    "counter": true
                  }
                ]
              }
            ]
          }
        ]
      }
    }
    """
    sample_config = json.loads(sample_config_str)

    # --- Convert config to commands ---
    generated_commands = config_to_commands(sample_config)
    print("--- Generated Commands from Config ---")
    for cmd in generated_commands:
        print(cmd)

