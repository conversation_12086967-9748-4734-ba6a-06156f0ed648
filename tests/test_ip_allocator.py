"""
Tests for IP allocator functionality.
"""

import logging
import pytest
import json
from unittest.mock import Mock, MagicMock
from v_switch.core_service.ip_allocator import (
    IPAllocator,
    IPAllocatorError,
    IPRangeExhaustedError,
    InvalidIPError
)
from v_switch.common.etcd_client import ETCDClient


class TestIPAllocator:
    """Test cases for IPAllocator class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_etcd_client = Mock(spec=ETCDClient)
        self.test_key = "/test/ip_allocator"
        self.ip_min = "*************"
        self.ip_max = "**************"
    
    def test_init_with_valid_ips(self):
        """Test initialization with valid IP addresses."""
        # Mock ETCD to return None (no existing data)
        self.mock_etcd_client.get_json.return_value = (None, None)
        self.mock_etcd_client.put_json.return_value = True
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        assert allocator.ip_min == self.ip_min
        assert allocator.ip_max == self.ip_max
        assert allocator.next_ip == self.ip_min
        assert len(allocator.allocated_ips) == 0
        assert len(allocator.reclaimed_ips) == 0
        
        # Verify ETCD calls
        self.mock_etcd_client.get_json.assert_called_once_with(self.test_key)
        self.mock_etcd_client.put_json.assert_called_once()
    
    def test_init_with_invalid_ip_min(self):
        """Test initialization with invalid minimum IP."""
        with pytest.raises(InvalidIPError):
            IPAllocator(
                "invalid_ip", 
                self.ip_max, 
                self.mock_etcd_client, 
                self.test_key
            )
    
    def test_init_with_invalid_ip_max(self):
        """Test initialization with invalid maximum IP."""
        with pytest.raises(InvalidIPError):
            IPAllocator(
                self.ip_min, 
                "invalid_ip", 
                self.mock_etcd_client, 
                self.test_key
            )
    
    def test_init_with_invalid_range(self):
        """Test initialization with invalid IP range (min >= max)."""
        with pytest.raises(InvalidIPError):
            IPAllocator(
                "**************", 
                "*************", 
                self.mock_etcd_client, 
                self.test_key
            )
    
    def test_init_with_existing_data(self):
        """Test initialization with existing ETCD data."""
        existing_data = {
            "ip_min": self.ip_min,
            "ip_max": self.ip_max,
            "next_ip": "*************",
            "allocated_ips": ["*************", "*************"],
            "reclaimed_ips": ["*************"]
        }
        
        self.mock_etcd_client.get_json.return_value = (existing_data, None)
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        assert allocator.next_ip == "*************"
        assert len(allocator.allocated_ips) == 2
        assert "*************" in allocator.allocated_ips
        assert "*************" in allocator.allocated_ips
        assert allocator.reclaimed_ips == ["*************"]
    
    def test_allocate_ip_from_reclaimed(self):
        """Test IP allocation from reclaimed list."""
        # Setup allocator with reclaimed IPs
        existing_data = {
            "ip_min": self.ip_min,
            "ip_max": self.ip_max,
            "next_ip": "*************",
            "allocated_ips": ["*************"],
            "reclaimed_ips": ["*************", "*************"]
        }
        
        self.mock_etcd_client.get_json.return_value = (existing_data, None)
        self.mock_etcd_client.put_json.return_value = True
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        # Allocate IP
        allocated_ip = allocator.allocate_ip()
        
        assert allocated_ip == "*************"
        assert allocated_ip in allocator.allocated_ips
        assert allocated_ip not in allocator.reclaimed_ips
        assert len(allocator.reclaimed_ips) == 1
        assert "*************" in allocator.reclaimed_ips
    
    def test_allocate_ip_from_next_ip(self):
        """Test IP allocation from next_ip when reclaimed list is empty."""
        self.mock_etcd_client.get_json.return_value = (None, None)
        self.mock_etcd_client.put_json.return_value = True
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        # Allocate first IP
        allocated_ip = allocator.allocate_ip()
        
        assert allocated_ip == "*************"
        assert allocated_ip in allocator.allocated_ips
        assert allocator.next_ip == "*************"
    
    def test_allocate_ip_skip_allocated(self):
        """Test IP allocation skips already allocated IPs."""
        existing_data = {
            "ip_min": self.ip_min,
            "ip_max": self.ip_max,
            "next_ip": "*************",
            "allocated_ips": ["*************", "*************"],
            "reclaimed_ips": []
        }
        
        self.mock_etcd_client.get_json.return_value = (existing_data, None)
        self.mock_etcd_client.put_json.return_value = True
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        # Allocate IP
        allocated_ip = allocator.allocate_ip()
        
        assert allocated_ip == "*************"
        assert allocated_ip in allocator.allocated_ips
    
    def test_allocate_ip_range_exhausted(self):
        """Test IP allocation when range is exhausted."""
        # Create allocator with small range
        small_range_min = "*************"
        small_range_max = "*************"
        
        existing_data = {
            "ip_min": small_range_min,
            "ip_max": small_range_max,
            "next_ip": small_range_min,
            "allocated_ips": ["*************", "*************"],
            "reclaimed_ips": []
        }
        
        self.mock_etcd_client.get_json.return_value = (existing_data, None)
        
        allocator = IPAllocator(
            small_range_min, 
            small_range_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        # Should raise exception when trying to allocate
        with pytest.raises(IPRangeExhaustedError):
            allocator.allocate_ip()
    
    def test_release_ip_success(self):
        """Test successful IP release."""
        existing_data = {
            "ip_min": self.ip_min,
            "ip_max": self.ip_max,
            "next_ip": "*************",
            "allocated_ips": ["*************", "*************"],
            "reclaimed_ips": []
        }
        
        self.mock_etcd_client.get_json.return_value = (existing_data, None)
        self.mock_etcd_client.put_json.return_value = True
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        # Release IP
        result = allocator.release_ip("*************")
        
        assert result is True
        assert "*************" not in allocator.allocated_ips
        assert "*************" in allocator.reclaimed_ips
    
    def test_release_ip_not_allocated(self):
        """Test releasing IP that is not allocated."""
        self.mock_etcd_client.get_json.return_value = (None, None)
        self.mock_etcd_client.put_json.return_value = True
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        # Try to release unallocated IP
        result = allocator.release_ip("*************")
        
        assert result is False
    
    def test_release_ip_invalid_format(self):
        """Test releasing IP with invalid format."""
        self.mock_etcd_client.get_json.return_value = (None, None)
        self.mock_etcd_client.put_json.return_value = True
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        # Try to release invalid IP
        result = allocator.release_ip("invalid_ip")
        
        assert result is False
    
    def test_release_ip_out_of_range(self):
        """Test releasing IP that is out of allocation range."""
        self.mock_etcd_client.get_json.return_value = (None, None)
        self.mock_etcd_client.put_json.return_value = True
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        # Try to release IP outside range
        result = allocator.release_ip("*************")
        
        assert result is False
    
    def test_get_allocation_status(self):
        """Test getting allocation status."""
        existing_data = {
            "ip_min": self.ip_min,
            "ip_max": self.ip_max,
            "next_ip": "*************",
            "allocated_ips": ["*************", "*************"],
            "reclaimed_ips": ["*************"]
        }
        
        self.mock_etcd_client.get_json.return_value = (existing_data, None)
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        status = allocator.get_allocation_status()
        
        assert status["ip_range"] == f"{self.ip_min}-{self.ip_max}"
        assert status["total_ips"] == 9  # ************* to **************
        assert status["allocated_count"] == 2
        assert status["reclaimed_count"] == 1
        assert status["available_count"] == 7
        assert status["next_ip"] == "*************"
        assert status["utilization_rate"] == pytest.approx(22.22, rel=1e-2)
    
    def test_is_ip_allocated(self):
        """Test checking if IP is allocated."""
        existing_data = {
            "ip_min": self.ip_min,
            "ip_max": self.ip_max,
            "next_ip": "*************",
            "allocated_ips": ["*************", "*************"],
            "reclaimed_ips": []
        }
        
        self.mock_etcd_client.get_json.return_value = (existing_data, None)
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        assert allocator.is_ip_allocated("*************") is True
        assert allocator.is_ip_allocated("*************") is False
    
    def test_get_allocated_ips(self):
        """Test getting allocated IPs list."""
        existing_data = {
            "ip_min": self.ip_min,
            "ip_max": self.ip_max,
            "next_ip": "*************",
            "allocated_ips": ["*************", "*************"],
            "reclaimed_ips": []
        }
        
        self.mock_etcd_client.get_json.return_value = (existing_data, None)
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        allocated_ips = allocator.get_allocated_ips()
        
        assert len(allocated_ips) == 2
        assert "*************" in allocated_ips
        assert "*************" in allocated_ips
    
    def test_get_reclaimed_ips(self):
        """Test getting reclaimed IPs list."""
        existing_data = {
            "ip_min": self.ip_min,
            "ip_max": self.ip_max,
            "next_ip": "*************",
            "allocated_ips": ["*************"],
            "reclaimed_ips": ["*************", "*************"]
        }
        
        self.mock_etcd_client.get_json.return_value = (existing_data, None)
        
        allocator = IPAllocator(
            self.ip_min, 
            self.ip_max, 
            self.mock_etcd_client, 
            self.test_key
        )
        
        reclaimed_ips = allocator.get_reclaimed_ips()
        
        assert len(reclaimed_ips) == 2
        assert "*************" in reclaimed_ips
        assert "*************" in reclaimed_ips
