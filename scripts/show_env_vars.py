#!/usr/bin/env python3
"""
Utility script to show all available environment variables for configuration override.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from v_switch.config.server_config import ServerConfig
from v_switch.config.agent_config import AgentConfig


def main():
    """Main function to display environment variable help."""
    print("V-Switch Configuration Environment Variables")
    print("=" * 50)
    print()
    
    print("SERVER CONFIGURATION:")
    print("-" * 30)
    server_config = ServerConfig()
    print(server_config.get_env_var_help())
    
    print("\nAGENT CONFIGURATION:")
    print("-" * 30)
    agent_config = AgentConfig()
    print(agent_config.get_env_var_help())
    
    print("\nUsage Examples:")
    print("-" * 15)
    print("# Set ETCD connection")
    print("export VSWITCH_ETCD_HOST=*************")
    print("export VSWITCH_ETCD_PORT=2379")
    print()
    print("# Set server port")
    print("export VSWITCH_SERVER_PORT=8080")
    print()
    print("# Set logging level")
    print("export VSWITCH_LOGGING_LEVEL=DEBUG")
    print()
    print("# Set agent ID")
    print("export VSWITCH_AGENT_ID=agent-001")
    print()
    print("# Run with environment variables")
    print("python -m v_switch.server")
    print("# or")
    print("python -m v_switch.agent")


if __name__ == "__main__":
    main()
