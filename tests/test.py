import threading
from typing import Dict


class Test:

    def __init__(self):
        self._subnet_watch: Dict[str, str] = {}

    def add_key(self, key: str, value: str):
        self._subnet_watch[key] = value

    def print_keys(self):
        keys = self._subnet_watch.keys()
        print(f"keys: {keys}")
        for key in keys:
            print(key)


if __name__ == "__main__":
    test = {
        "eip": None,
        "internal_ip": None,
        "rate": None,
        "ceil": None,
    }
    print(test)
