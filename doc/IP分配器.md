
## 初始化
初始化一个IP分配器实例，初始化参数：
- ip_min: 最小IP地址
- ip_max: 最大IP地址
- etcd_client: etcd客户端实例
- key: etcd中存储IP地址的key（不带前缀）
初始化逻辑：
  1. 从etcd中获取IP分配器数据，如果没有则创建一个空的数据
  2. 从数据中获取已分配的IP地址列表，记录到 allocated_ips 字段
  3. 从数据中获取已回收的IP地址列表，记录到 reclaimed_ips 字段
IP分配器数据格式：
```json
{
  "ip_min": "*************",
  "ip_max": "*************54",
  "next_ip": "*************",
  "allocated_ips": ["*************", "*************"],
  "reclaimed_ips": []
}
```

## 分配IP
执行分配地址方法时, 会执行以下逻辑：
1. 检查回收的IP地址列表是否有IP地址，如果有，则从回收的IP地址列表中取出第一个IP地址作为分配的IP地址，并将其从回收列表中删除
2. 如果回收的IP地址列表为空，则从下一个IP地址开始查找
3. 检查下一个IP地址是否在已分配的IP地址列表中
4. 如果下一个IP地址在已分配的IP地址列表中，则再次检查下一个IP地址，直到找到一个未分配的IP地址或超过最大IP地址
5. 如果找到了未分配的IP地址，则将其添加到已分配的IP地址列表
6. 如果没有找到未分配的IP地址，则返回错误信息
7. 更新etcd中的IP分配器数据，包括已分配的IP地址列表和下一个IP地址


## 释放IP
执行释放地址方法时, 会执行以下逻辑：
1. 检查要释放的IP地址是否在已分配的IP地址列表中
2. 如果在已分配的IP地址列表中，则将其从已分配的IP地址列表中删除
3. 将释放的IP地址添加到回收的IP地址列表中
4. 更新etcd中的IP分配器数据，包括已分配的IP地址列表和回收的IP地址列表

