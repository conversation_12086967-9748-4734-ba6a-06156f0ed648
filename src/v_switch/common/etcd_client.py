"""
ETCD client wrapper for v-switch components.
"""

import json
import logging
import time
from typing import Dict, Any, Generator, Optional, Callable, Tuple, Iterator
from contextlib import contextmanager
import etcd3
from etcd3.events import Event


class ETCDClient:
    """ETCD client wrapper with connection management and utilities."""
    
    def __init__(self, host: str = "localhost", port: int = 2379, 
                 timeout: float = 5.0, username: str = "", password: str = ""):
        """Initialize ETCD client.
        
        Args:
            host: ETCD server host
            port: ETCD server port
            timeout: Connection timeout
            username: ETCD username (optional)
            password: ETCD password (optional)
            
        Raises:
            ImportError: If etcd3 library is not available
        """

        self.host = host
        self.port = port
        self.timeout = timeout
        self.username = username
        self.password = password
        self.logger = logging.getLogger(__name__)
        self._client = None
        
    def connect(self) -> None:
        """Establish connection to ETCD server.

        Raises:
            Exception: If connection fails
        """

        try:
            auth = None
            if self.username and self.password:
                auth = (self.username, self.password)

            self._client = etcd3.client(
                host=self.host,
                port=self.port,
                timeout=self.timeout,
                user=self.username if auth else None,
                password=self.password if auth else None
            )

            # Test connection
            self._client.status()
            self.logger.info(f"Connected to ETCD at {self.host}:{self.port}")

        except Exception as e:
            self.logger.error(f"Failed to connect to ETCD: {e}")
            raise
    
    @property
    def client(self):
        """Get ETCD client, connecting if necessary."""
        if self._client is None:
            self.connect()
        return self._client
    
    def put(self, key: str, value: str, lease: Optional[int] = None) -> bool:
        """Put a key-value pair to ETCD.

        Args:
            key: Key to store
            value: Value to store
            lease: Optional lease ID

        Returns:
            True if successful
        """

        try:
            self.client.put(key, value, lease=lease)
            self.logger.debug(f"Put key: {key}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to put key {key}: {e}")
            return False
    
    def put_json(self, key: str, data: Dict[str, Any], lease: Optional[int] = None) -> bool:
        """Put JSON data to ETCD.
        
        Args:
            key: Key to store
            data: Data to store as JSON
            lease: Optional lease ID
            
        Returns:
            True if successful
        """
        try:
            value = json.dumps(data)
            return self.put(key, value, lease)
        except Exception as e:
            self.logger.error(f"Failed to put JSON to key {key}: {e}")
            return False
    
    def get(self, key: str) -> Tuple[Optional[str], Optional[Any]]:
        """Get value from ETCD.

        Args:
            key: Key to retrieve

        Returns:
            Tuple of (value, metadata) or (None, None) if not found
        """
        try:
            value, metadata = self.client.get(key)
            if value is not None:
                value = value.decode('utf-8')
                self.logger.debug(f"Got key: {key}")
            return value, metadata
        except Exception as e:
            self.logger.error(f"Failed to get key {key}: {e}")
            return None, None
    
    def get_json(self, key: str) -> Tuple[Optional[Dict[str, Any]], Optional[Any]]:
        """Get JSON data from ETCD.

        Args:
            key: Key to retrieve

        Returns:
            Tuple of (parsed_data, metadata) or (None, None) if not found
        """
        value, metadata = self.get(key)
        if value is not None:
            try:
                data = json.loads(value)
                return data, metadata
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse JSON from key {key}: {e}")
        return None, metadata

    def delete(self, key: str) -> bool:
        """Delete a key from ETCD.

        Args:
            key: Key to delete

        Returns:
            True if successful
        """
        try:
            self.client.delete(key)
            self.logger.debug(f"Deleted key: {key}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to delete key {key}: {e}")
            return False

    def get_prefix(self, prefix: str) -> Iterator[Tuple[str, str]]:
        """Get all key-value pairs with given prefix.

        Args:
            prefix: Key prefix to search

        Yields:
            Tuples of (key, value)
        """
        try:
            for value, metadata in self.client.get_prefix(prefix):
                key = metadata.key.decode('utf-8')
                value = value.decode('utf-8')
                yield key, value
        except Exception as e:
            self.logger.error(f"Failed to get prefix {prefix}: {e}")

    def create_lease(self, ttl: int) -> Optional[int]:
        """Create a lease with given TTL.

        Args:
            ttl: Time to live in seconds

        Returns:
            Lease ID or None if failed
        """
        try:
            lease = self.client.lease(ttl)
            self.logger.info(f"Created lease {lease.id} with TTL {ttl}")
            return lease.id
        except Exception as e:
            self.logger.error(f"Failed to create lease: {e}")
            return None
    
    def refresh_lease(self, lease_id: int) -> bool:
        """Refresh a lease.

        Args:
            lease_id: Lease ID to refresh

        Returns:
            True if successful
        """
        try:
            next(self.client.refresh_lease(lease_id))
            self.logger.debug(f"Lease {lease_id} refreshed successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to refresh lease {lease_id}: {e}")
            return False

    def watch_key(self, key: str) -> Generator[Any, Any, None]:
        """Watch a single key for changes.

        Args:
            key: Key to watch
            callback: Function to call on events
        """
        events_iterator, _ = self.client.watch(key)
        self.logger.info(f"Watching key: {key}")

        return events_iterator
    
    def watch_prefix(self, prefix: str) -> Generator[Any, Any, None]:
        """Watch a key prefix for changes.

        Args:
            prefix: Key prefix to watch
            callback: Function to call on events
        """
        events_iterator, _ = self.client.watch_prefix(prefix, prev_kv=True)
        self.logger.info(f"Watching prefix: {prefix}")

        return events_iterator

    @contextmanager
    def transaction(self):
        """Context manager for ETCD transactions.

        Yields:
            Transaction object
        """
        try:
            txn = self.client.transaction()
            yield txn
        except Exception as e:
            self.logger.error(f"Transaction failed: {e}")
            raise

    def ensure_directory(self, path: str) -> bool:
        """Ensure a directory path exists in ETCD.

        Args:
            path: Directory path to ensure

        Returns:
            True if successful
        """
        try:
            # ETCD doesn't have directories, but we can put an empty value
            # to ensure the path exists
            self.put(f"{path.rstrip('/')}/", "")
            return True
        except Exception as e:
            self.logger.error(f"Failed to ensure directory {path}: {e}")
            return False
