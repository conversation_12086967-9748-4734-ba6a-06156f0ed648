#!/usr/bin/env python3
"""
Example script demonstrating the FastAPI v-switch API server.
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from v_switch.config.server_config import ServerConfig
from v_switch.core_service.core_service import CoreService
from v_switch.api_server.api_server import APIServer, create_fastapi_app


async def run_standalone_app():
    """Run FastAPI app standalone for development."""
    print("Creating standalone FastAPI app...")
    
    # Load configuration
    config_file = "config/server_config.yaml"
    if not os.path.exists(config_file):
        print(f"Configuration file not found: {config_file}")
        return
    
    config = ServerConfig.from_file(config_file)
    
    # Create core service (mock for this example)
    core_service = CoreService(config)
    
    # Create FastAPI app
    app = create_fastapi_app(core_service)
    
    print("FastAPI app created successfully!")
    print("You can now run it with uvicorn:")
    print(f"uvicorn examples.fastapi_example:app --host 0.0.0.0 --port {config.server.port}")
    
    return app


def run_full_server():
    """Run the full v-switch server with FastAPI."""
    print("Starting full v-switch server with FastAPI...")
    
    try:
        # Load configuration
        config_file = "config/server_config.yaml"
        if not os.path.exists(config_file):
            print(f"Configuration file not found: {config_file}")
            return 1
        
        config = ServerConfig.from_file(config_file)
        print(f"Configuration loaded from {config_file}")
        
        # Create core service
        core_service = CoreService(config)
        print("Core service created")
        
        # Create API server
        api_server = APIServer(config, core_service)
        print("API server created")
        
        # Start core service
        if not core_service.start():
            print("Failed to start core service")
            return 1
        print("Core service started")
        
        # Start API server
        if not api_server.start():
            print("Failed to start API server")
            core_service.stop()
            return 1
        print(f"FastAPI server started on port {config.server.port}")
        
        print("\nAPI Documentation available at:")
        print(f"  Swagger UI: http://localhost:{config.server.port}/docs")
        print(f"  ReDoc:      http://localhost:{config.server.port}/redoc")
        print(f"  OpenAPI:    http://localhost:{config.server.port}/openapi.json")
        
        print("\nPress Ctrl+C to stop the server...")
        
        try:
            # Keep the server running
            while api_server.is_running():
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nShutting down...")
        
        # Stop services
        api_server.stop()
        core_service.stop()
        print("Services stopped")
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


def main():
    """Main function."""
    if len(sys.argv) > 1 and sys.argv[1] == "standalone":
        # Create app for uvicorn
        app = asyncio.run(run_standalone_app())
        return app
    else:
        # Run full server
        return run_full_server()


# For uvicorn: uvicorn examples.fastapi_example:app
app = None
if __name__ != "__main__":
    # When imported by uvicorn
    import asyncio
    app = asyncio.run(run_standalone_app())

if __name__ == "__main__":
    sys.exit(main())
